import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/follow_service.dart';
import '../controllers/admin_auth_controller.dart';
import '../controllers/user_management_controller.dart';
import '../controllers/wallet_management_controller.dart';
import '../theme/admin_theme.dart';
import '../widgets/mobile_admin_layout.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/recent_activities_widget.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final _adminAuthController = Get.find<AdminAuthController>();
  final _userManagementController = Get.put(UserManagementController());
  final _walletController = Get.put(WalletManagementController());

  @override
  Widget build(BuildContext context) {
    return MobileAdminLayout(
      title: 'Dashboard',
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(),
            const SizedBox(height: AdminTheme.spacingLarge),

            // Stats Cards
            _buildStatsSection(),
            const SizedBox(height: AdminTheme.spacingLarge),

            // Recent Activities
            _buildRecentActivitiesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Obx(() => Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.deepPurple, Colors.deepPurple.shade300],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back, ${_adminAuthController.currentAdmin?.name ?? 'Admin'}!',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Here\'s what\'s happening with Money Mouthy today.',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.dashboard,
                color: Colors.white,
                size: 48,
              ),
            ],
          ),
        ));
  }

  Widget _buildStatsSection() {
    return Obx(() {
      final screenWidth = MediaQuery.of(context).size.width;
      final isMobile = screenWidth < 768;

      if (isMobile) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Total Users',
                    value: _userManagementController.totalUsers.toString(),
                    icon: Icons.people,
                    color: AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingSmall),
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Active Users',
                    value: _userManagementController.activeUsers.toString(),
                    icon: Icons.person,
                    color: AdminTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AdminTheme.spacingSmall),
            Row(
              children: [
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Blocked Users',
                    value: _userManagementController.blockedUsers.toString(),
                    icon: Icons.block,
                    color: AdminTheme.errorColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingSmall),
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Stripe Balance',
                    value:
                        '\$${_walletController.totalStripePayments.toStringAsFixed(2)}',
                    icon: Icons.payment,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Total Users',
                    value: _userManagementController.totalUsers.toString(),
                    icon: Icons.people,
                    color: AdminTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Active Users',
                    value: _userManagementController.activeUsers.toString(),
                    icon: Icons.person,
                    color: AdminTheme.successColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Blocked Users',
                    value: _userManagementController.blockedUsers.toString(),
                    icon: Icons.block,
                    color: AdminTheme.errorColor,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: DashboardStatsCard(
                    title: 'Stripe Balance',
                    value:
                        '\$${_walletController.totalStripePayments.toStringAsFixed(2)}',
                    icon: Icons.payment,
                    color: Colors.purple,
                  ),
                ),
                const SizedBox(width: AdminTheme.spacingMedium),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      print('Starting follow process...');
                      final followService = FollowService();
                      debugPrint('Follow service: $followService');
                      final result =
                          await followService.makeAllUsersFollowEachOther();

                      if (result['success']) {
                        print('Success: ${result['message']}\n');
                        print('Processed ${result['processedUsers']} users\n');
                        print(
                            'Created ${result['totalRelationships']} relationships\n');
                      } else {
                        print('Error: ${result['message']}\n');
                      }
                    },
                    child: const Text('Make All Users Follow Each Other'),
                  ),
                ),
              ],
            ),
          ],
        );
      }
    });
  }

  Widget _buildRecentActivitiesSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const RecentActivitiesWidget(),
    );
  }
}
